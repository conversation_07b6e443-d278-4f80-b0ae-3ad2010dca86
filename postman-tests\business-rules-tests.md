# 🚨 Regras de Negócio - Testes de Exception

## 📋 **9 Regras Implementadas (3 por Entidade)**

### **CUSTOMER (3 regras) ✅**
1. **CREATE**: Não permitir emails duplicados → `409 Conflict`
2. **UPDATE**: Não permitir downgrade VIP→regular → `400 Bad Request`
3. **DELETE**: Não permitir deletar clientes com vendas → `400 Bad Request`

### **PHONE (3 regras) ✅**
4. **CREATE**: Não permitir modelos duplicados na mesma marca → `409 Conflict`
5. **UPDATE**: Não permitir redução de preço > 50% → `400 Bad Request`
6. **DELETE**: Não permitir deletar phones em vendas → `400 Bad Request`

### **ACCESSORY (3 regras) ✅**
7. **CREATE**: Não permitir preço negativo → `400 Bad Request`
8. **READ**: <PERSON>rro quando não há acessórios em estoque → `400 Bad Request`
9. **UPDATE**: Não reduzir estoque abaixo de 0 → `400 Bad Request`

---

## 🧪 **Sequência de Testes Completa**

### **🔥 CUSTOMER - 3 Regras**

#### **REGRA 1: Email Duplicado (CREATE)**
```bash
# Teste 1A: Criar cliente original
POST /customers
{
  "name": "Cliente Original",
  "email": "<EMAIL>",
  "phone": "(11) 99999-1111",
  "birthDate": "1990-01-01",
  "address": "Rua A, 123",
  "customerType": "regular",
  "active": true
}
# Expected: 201 Created

# Teste 1B: Tentar duplicar email (deve falhar)
POST /customers
{
  "name": "Cliente Duplicado",
  "email": "<EMAIL>",
  "phone": "(11) 99999-2222",
  "birthDate": "1985-05-15",
  "address": "Rua B, 456",
  "customerType": "premium",
  "active": true
}
# Expected: 409 Conflict - "Email '<EMAIL>' já está em uso"
```

#### **REGRA 2: Downgrade VIP (UPDATE)**
```bash
# Teste 2A: Criar cliente VIP
POST /customers
{
  "name": "Cliente VIP",
  "email": "<EMAIL>",
  "phone": "(11) 99999-8888",
  "birthDate": "1980-01-01",
  "address": "Rua VIP, 123",
  "customerType": "vip",
  "active": true
}
# Expected: 201 Created

# Teste 2B: Tentar downgrade para regular (deve falhar)
PATCH /customers/[ID_DO_CLIENTE_VIP]
{
  "customerType": "regular"
}
# Expected: 400 Bad Request - "Não é permitido fazer downgrade de VIP para regular"
```

#### **REGRA 3: Deletar Cliente com Vendas (DELETE)**
```bash
# Teste 3A: Criar uma venda para o cliente
POST /sales
{
  "customerId": [ID_DO_CLIENTE],
  "storeId": 1,
  "paymentMethod": "pix",
  "seller": "Vendedor Teste",
  "items": [
    {
      "productId": 1,
      "productType": "phone",
      "quantity": 1,
      "unitPrice": 1000.00,
      "subtotal": 1000.00
    }
  ]
}
# Expected: 201 Created

# Teste 3B: Tentar deletar cliente com vendas (deve falhar)
DELETE /customers/[ID_DO_CLIENTE]
# Expected: 400 Bad Request - "Não é possível deletar cliente com vendas associadas"
```

### **📱 PHONE - 3 Regras**

#### **REGRA 4: Modelo Duplicado na Mesma Marca (CREATE)**
```bash
# Teste 4A: Criar celular original
POST /phones
{
  "model": "Galaxy Teste",
  "image": "https://example.com/test.jpg",
  "releaseDate": "2024-01-01",
  "price": 2999.99,
  "category": "Smartphone",
  "brandId": 1
}
# Expected: 201 Created

# Teste 4B: Tentar duplicar na mesma marca (deve falhar)
POST /phones
{
  "model": "Galaxy Teste",
  "image": "https://example.com/test2.jpg",
  "releaseDate": "2024-02-01",
  "price": 3299.99,
  "category": "Smartphone",
  "brandId": 1
}
# Expected: 409 Conflict - "Celular 'Galaxy Teste' já existe para esta marca"

# Teste 4C: Mesmo modelo em marca diferente (deve funcionar)
POST /phones
{
  "model": "Galaxy Teste",
  "image": "https://example.com/test3.jpg",
  "releaseDate": "2024-01-01",
  "price": 2999.99,
  "category": "Smartphone",
  "brandId": 2
}
# Expected: 201 Created
```

#### **REGRA 5: Redução de Preço Excessiva (UPDATE)**
```bash
# Teste 5A: Criar celular com preço alto
POST /phones
{
  "model": "iPhone Teste Preço",
  "image": "https://example.com/iphone.jpg",
  "releaseDate": "2024-01-01",
  "price": 10000.00,
  "category": "Smartphone",
  "brandId": 2
}
# Expected: 201 Created

# Teste 5B: Tentar reduzir mais de 50% (deve falhar)
PATCH /phones/[ID_DO_CELULAR]
{
  "price": 4000.00
}
# Expected: 400 Bad Request - "Redução de preço de 60.0% não permitida"

# Teste 5C: Reduzir dentro do limite (deve funcionar)
PATCH /phones/[ID_DO_CELULAR]
{
  "price": 6000.00
}
# Expected: 200 OK (40% de redução)
```

#### **REGRA 6: Deletar Phone em Vendas (DELETE)**
```bash
# Teste 6A: Criar uma venda com o phone
POST /sales
{
  "customerId": 1,
  "storeId": 1,
  "paymentMethod": "pix",
  "seller": "Vendedor Teste",
  "items": [
    {
      "productId": [ID_DO_PHONE],
      "productType": "phone",
      "quantity": 1,
      "unitPrice": 2999.99,
      "subtotal": 2999.99
    }
  ]
}
# Expected: 201 Created

# Teste 6B: Tentar deletar phone em vendas (deve falhar)
DELETE /phones/[ID_DO_PHONE]
# Expected: 400 Bad Request - "Não é possível deletar celular que está associado a vendas"
```

### **🔌 ACCESSORY - 3 Regras**

#### **REGRA 7: Preço Negativo (CREATE)**
```bash
# Teste 7A: Tentar criar acessório com preço negativo (deve falhar)
POST /accessories
{
  "name": "Acessório Teste Negativo",
  "description": "Teste de preço negativo",
  "price": -50.00,
  "category": "Teste",
  "image": "https://example.com/teste.jpg",
  "stock": 10
}
# Expected: 400 Bad Request - "Preço não pode ser negativo. Valor informado: R$ -50"

# Teste 7B: Criar acessório com preço zero (deve funcionar)
POST /accessories
{
  "name": "Acessório Grátis",
  "description": "Acessório promocional",
  "price": 0.00,
  "category": "Promocional",
  "image": "https://example.com/gratis.jpg",
  "stock": 5
}
# Expected: 201 Created
```

#### **REGRA 8: Sem Estoque Disponível (READ)**
```bash
# Teste 8A: Zerar estoque de todos os acessórios
PATCH /accessories/1/stock
{
  "quantity": -100
}
# Repita para todos os acessórios

# Teste 8B: Tentar buscar apenas com estoque (deve falhar)
GET /accessories/stock/only-available
# Expected: 400 Bad Request - "Nenhum acessório disponível em estoque no momento"

# Teste 8C: Adicionar estoque e testar novamente
PATCH /accessories/1/stock
{
  "quantity": 10
}

GET /accessories/stock/only-available
# Expected: 200 OK com lista de acessórios
```

#### **REGRA 9: Estoque Negativo (UPDATE)**
```bash
# Teste 9A: Criar acessório com estoque baixo
POST /accessories
{
  "name": "Acessório Estoque Baixo",
  "description": "Para teste de estoque",
  "price": 99.99,
  "category": "Teste",
  "image": "https://example.com/estoque.jpg",
  "stock": 5
}
# Expected: 201 Created

# Teste 9B: Tentar reduzir estoque além do disponível (deve falhar)
PATCH /accessories/[ID_DO_ACESSORIO]/stock
{
  "quantity": -10
}
# Expected: 400 Bad Request - "Operação resultaria em estoque negativo. Estoque atual: 5, Quantidade solicitada: -10, Resultado: -5"

# Teste 9C: Reduzir estoque dentro do limite (deve funcionar)
PATCH /accessories/[ID_DO_ACESSORIO]/stock
{
  "quantity": -3
}
# Expected: 200 OK (estoque fica com 2)
```

### **4. REGRA 4: Acessórios sem Estoque**
```bash
# Teste 4A: Zerar estoque de todos os acessórios
PATCH /accessories/1/stock
{
  "quantity": -100
}
# Repita para todos os acessórios

# Teste 4B: Tentar buscar apenas com estoque (deve falhar)
GET /accessories/stock/only-available
# Expected: 400 Bad Request

# Teste 4C: Adicionar estoque e testar novamente
PATCH /accessories/1/stock
{
  "quantity": 10
}

GET /accessories/stock/only-available
# Expected: 200 OK com lista de acessórios
```

### **5. REGRA 5: Lojas Inativas**
```bash
# Teste 5A: Desativar todas as lojas
PATCH /stores/1
{
  "status": "inactive"
}
# Repita para todas as lojas

# Teste 5B: Tentar buscar apenas ativas (deve falhar)
GET /stores/active/only
# Expected: 400 Bad Request

# Teste 5C: Ativar uma loja e testar novamente
PATCH /stores/1
{
  "status": "active"
}

GET /stores/active/only
# Expected: 200 OK com lista de lojas ativas
```

### **6. REGRA 7: Redução de Preço Excessiva**
```bash
# Teste 7A: Criar celular com preço alto
POST /phones
{
  "model": "iPhone Teste Preço",
  "image": "https://example.com/iphone.jpg",
  "releaseDate": "2024-01-01",
  "price": 10000.00,
  "category": "Smartphone",
  "brandId": 2
}
# Expected: 201 Created

# Teste 7B: Tentar reduzir mais de 50% (deve falhar)
PATCH /phones/[ID_DO_CELULAR]
{
  "price": 4000.00
}
# Expected: 400 Bad Request (60% de redução)

# Teste 7C: Reduzir dentro do limite (deve funcionar)
PATCH /phones/[ID_DO_CELULAR]
{
  "price": 6000.00
}
# Expected: 200 OK (40% de redução)
```

### **7. REGRA 8: Downgrade VIP**
```bash
# Teste 8A: Criar cliente VIP
POST /customers
{
  "name": "Cliente VIP",
  "email": "<EMAIL>",
  "phone": "(11) 99999-8888",
  "birthDate": "1980-01-01",
  "address": "Rua VIP, 123",
  "customerType": "vip",
  "active": true
}
# Expected: 201 Created

# Teste 8B: Tentar downgrade para regular (deve falhar)
PATCH /customers/[ID_DO_CLIENTE_VIP]
{
  "customerType": "regular"
}
# Expected: 400 Bad Request

# Teste 8C: Downgrade para premium (deve funcionar)
PATCH /customers/[ID_DO_CLIENTE_VIP]
{
  "customerType": "premium"
}
# Expected: 200 OK
```

---

## 📊 **Códigos de Status Esperados**

| Entidade | Operação | Regra | Status | Mensagem |
|----------|----------|-------|--------|----------|
| Customer | CREATE | Email duplicado | 409 | "Email 'X' já está em uso" |
| Customer | UPDATE | Downgrade VIP | 400 | "Não é permitido fazer downgrade de VIP para regular" |
| Customer | DELETE | Cliente com vendas | 400 | "Não é possível deletar cliente com vendas associadas" |
| Phone | CREATE | Modelo duplicado | 409 | "Celular 'X' já existe para esta marca" |
| Phone | UPDATE | Redução excessiva | 400 | "Redução de preço de X% não permitida" |
| Phone | DELETE | Phone em vendas | 400 | "Não é possível deletar celular que está associado a vendas" |
| Accessory | CREATE | Preço negativo | 400 | "Preço não pode ser negativo" |
| Accessory | READ | Sem estoque | 400 | "Nenhum acessório disponível em estoque no momento" |
| Accessory | UPDATE | Estoque negativo | 400 | "Operação resultaria em estoque negativo" |

---

## 🔍 **Como Testar no Postman**

1. **Importe a collection** `postman-collection.json`
2. **Execute os testes na sequência** mostrada acima
3. **Verifique os códigos de status** e mensagens de erro
4. **Confirme que as regras** estão funcionando corretamente

## ⚠️ **Observações Importantes**

- **Ordem dos testes**: Execute na sequência para evitar conflitos
- **IDs dinâmicos**: Substitua `[ID_DO_CELULAR]` pelos IDs reais
- **Reset de dados**: Pode ser necessário resetar dados entre testes
- **Logs do servidor**: Verifique os logs para detalhes dos erros
