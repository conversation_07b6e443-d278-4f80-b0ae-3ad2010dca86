# 🚨 Regras de Negócio - Testes de Exception

## 📋 **Resumo das 9 Regras Implementadas**

### **CREATE (3 regras)**
1. **Brand**: Não permitir marcas duplicadas → `409 Conflict`
2. **Phone**: Não permitir celulares com mesmo modelo da mesma marca → `409 Conflict`
3. **Customer**: Não permitir emails duplicados → `409 Conflict`

### **READ (3 regras)**
4. **Accessory**: <PERSON>rro quando não há acessórios em estoque → `400 Bad Request`
5. **Store**: Erro quando não há lojas ativas → `400 Bad Request`
6. **Sale**: *(A implementar)* Não permitir consulta de vendas canceladas por clientes regulares

### **UPDATE (3 regras)**
7. **Phone**: Não permitir redução de preço superior a 50% → `400 Bad Request`
8. **Customer**: Não permitir downgrade de VIP para regular → `400 Bad Request`
9. **Sale**: *(A implementar)* Não permitir alteração de vendas já completadas

---

## 🧪 **Sequência de Testes Completa**

### **1. REGRA 1: <PERSON>as Duplicadas**
```bash
# Teste 1A: Criar marca original
POST /brands
{
  "name": "Teste Duplicação",
  "country": "Brasil"
}
# Expected: 201 Created

# Teste 1B: Tentar duplicar (deve falhar)
POST /brands
{
  "name": "Teste Duplicação",
  "country": "Argentina"
}
# Expected: 409 Conflict
```

### **2. REGRA 2: Modelos de Celular Duplicados**
```bash
# Teste 2A: Criar celular original
POST /phones
{
  "model": "Galaxy Teste",
  "image": "https://example.com/test.jpg",
  "releaseDate": "2024-01-01",
  "price": 2999.99,
  "category": "Smartphone",
  "brandId": 1
}
# Expected: 201 Created

# Teste 2B: Tentar duplicar na mesma marca (deve falhar)
POST /phones
{
  "model": "Galaxy Teste",
  "image": "https://example.com/test2.jpg",
  "releaseDate": "2024-02-01",
  "price": 3299.99,
  "category": "Smartphone",
  "brandId": 1
}
# Expected: 409 Conflict

# Teste 2C: Mesmo modelo em marca diferente (deve funcionar)
POST /phones
{
  "model": "Galaxy Teste",
  "image": "https://example.com/test3.jpg",
  "releaseDate": "2024-01-01",
  "price": 2999.99,
  "category": "Smartphone",
  "brandId": 2
}
# Expected: 201 Created
```

### **3. REGRA 3: Emails Duplicados**
```bash
# Teste 3A: Criar cliente original
POST /customers
{
  "name": "Cliente Original",
  "email": "<EMAIL>",
  "phone": "(11) 99999-1111",
  "birthDate": "1990-01-01",
  "address": "Rua A, 123",
  "customerType": "regular",
  "active": true
}
# Expected: 201 Created

# Teste 3B: Tentar duplicar email (deve falhar)
POST /customers
{
  "name": "Cliente Duplicado",
  "email": "<EMAIL>",
  "phone": "(11) 99999-2222",
  "birthDate": "1985-05-15",
  "address": "Rua B, 456",
  "customerType": "premium",
  "active": true
}
# Expected: 409 Conflict
```

### **4. REGRA 4: Acessórios sem Estoque**
```bash
# Teste 4A: Zerar estoque de todos os acessórios
PATCH /accessories/1/stock
{
  "quantity": -100
}
# Repita para todos os acessórios

# Teste 4B: Tentar buscar apenas com estoque (deve falhar)
GET /accessories/stock/only-available
# Expected: 400 Bad Request

# Teste 4C: Adicionar estoque e testar novamente
PATCH /accessories/1/stock
{
  "quantity": 10
}

GET /accessories/stock/only-available
# Expected: 200 OK com lista de acessórios
```

### **5. REGRA 5: Lojas Inativas**
```bash
# Teste 5A: Desativar todas as lojas
PATCH /stores/1
{
  "status": "inactive"
}
# Repita para todas as lojas

# Teste 5B: Tentar buscar apenas ativas (deve falhar)
GET /stores/active/only
# Expected: 400 Bad Request

# Teste 5C: Ativar uma loja e testar novamente
PATCH /stores/1
{
  "status": "active"
}

GET /stores/active/only
# Expected: 200 OK com lista de lojas ativas
```

### **6. REGRA 7: Redução de Preço Excessiva**
```bash
# Teste 7A: Criar celular com preço alto
POST /phones
{
  "model": "iPhone Teste Preço",
  "image": "https://example.com/iphone.jpg",
  "releaseDate": "2024-01-01",
  "price": 10000.00,
  "category": "Smartphone",
  "brandId": 2
}
# Expected: 201 Created

# Teste 7B: Tentar reduzir mais de 50% (deve falhar)
PATCH /phones/[ID_DO_CELULAR]
{
  "price": 4000.00
}
# Expected: 400 Bad Request (60% de redução)

# Teste 7C: Reduzir dentro do limite (deve funcionar)
PATCH /phones/[ID_DO_CELULAR]
{
  "price": 6000.00
}
# Expected: 200 OK (40% de redução)
```

### **7. REGRA 8: Downgrade VIP**
```bash
# Teste 8A: Criar cliente VIP
POST /customers
{
  "name": "Cliente VIP",
  "email": "<EMAIL>",
  "phone": "(11) 99999-8888",
  "birthDate": "1980-01-01",
  "address": "Rua VIP, 123",
  "customerType": "vip",
  "active": true
}
# Expected: 201 Created

# Teste 8B: Tentar downgrade para regular (deve falhar)
PATCH /customers/[ID_DO_CLIENTE_VIP]
{
  "customerType": "regular"
}
# Expected: 400 Bad Request

# Teste 8C: Downgrade para premium (deve funcionar)
PATCH /customers/[ID_DO_CLIENTE_VIP]
{
  "customerType": "premium"
}
# Expected: 200 OK
```

---

## 📊 **Códigos de Status Esperados**

| Regra | Cenário | Status | Mensagem |
|-------|---------|--------|----------|
| 1 | Marca duplicada | 409 | "Marca 'X' já existe no sistema" |
| 2 | Modelo duplicado | 409 | "Celular 'X' já existe para esta marca" |
| 3 | Email duplicado | 409 | "Email 'X' já está em uso" |
| 4 | Sem estoque | 400 | "Nenhum acessório disponível em estoque" |
| 5 | Sem lojas ativas | 400 | "Nenhuma loja ativa disponível" |
| 7 | Redução excessiva | 400 | "Redução de preço de X% não permitida" |
| 8 | Downgrade VIP | 400 | "Não é permitido fazer downgrade de VIP" |

---

## 🔍 **Como Testar no Postman**

1. **Importe a collection** `postman-collection.json`
2. **Execute os testes na sequência** mostrada acima
3. **Verifique os códigos de status** e mensagens de erro
4. **Confirme que as regras** estão funcionando corretamente

## ⚠️ **Observações Importantes**

- **Ordem dos testes**: Execute na sequência para evitar conflitos
- **IDs dinâmicos**: Substitua `[ID_DO_CELULAR]` pelos IDs reais
- **Reset de dados**: Pode ser necessário resetar dados entre testes
- **Logs do servidor**: Verifique os logs para detalhes dos erros
