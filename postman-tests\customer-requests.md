# 👥 Customer (Clientes) - <PERSON>quis<PERSON><PERSON><PERSON>es Postman

## Base URL
```
http://localhost:3000
```

## 📋 CRUD Operations

### 1. CREATE - <PERSON><PERSON>r Novo Cliente
**POST** `/customers`

**Headers:**
```
Content-Type: application/json
```

**Body (JSON):**
```json
{
  "name": "<PERSON>",
  "email": "<EMAIL>",
  "phone": "(11) 99999-5555",
  "birthDate": "1995-07-20",
  "address": "R<PERSON> das Palmeiras, 456 - Vila Madalena",
  "customerType": "premium",
  "active": true
}
```

**Expected Response (201):**
```json
{
  "id": 5,
  "name": "<PERSON>",
  "email": "<EMAIL>",
  "phone": "(11) 99999-5555",
  "birthDate": "1995-07-20",
  "address": "R<PERSON> das Palmeiras, 456 - <PERSON><PERSON> Madalena",
  "customerType": "premium",
  "active": true
}
```

---

### 2. READ ALL - Listar Todos os Clientes
**GET** `/customers`

**Expected Response (200):**
```json
[
  {
    "id": 1,
    "name": "Ana Silva",
    "email": "<EMAIL>",
    "phone": "(11) 99999-1111",
    "birthDate": "1990-05-15",
    "address": "Rua A, 123",
    "customerType": "premium",
    "active": true,
    "sales": [...]
  }
]
```

---

### 3. READ ONE - Buscar Cliente por ID
**GET** `/customers/1`

**Expected Response (200):**
```json
{
  "id": 1,
  "name": "Ana Silva",
  "email": "<EMAIL>",
  "phone": "(11) 99999-1111",
  "birthDate": "1990-05-15",
  "address": "Rua A, 123",
  "customerType": "premium",
  "active": true,
  "sales": [...]
}
```

---

### 4. READ BY EMAIL - Buscar Cliente por Email
**GET** `/customers/email/<EMAIL>`

**Expected Response (200):**
```json
{
  "id": 1,
  "name": "Ana Silva",
  "email": "<EMAIL>",
  "phone": "(11) 99999-1111",
  "birthDate": "1990-05-15",
  "address": "Rua A, 123",
  "customerType": "premium",
  "active": true
}
```

---

### 5. UPDATE - Atualizar Cliente
**PATCH** `/customers/1`

**Headers:**
```
Content-Type: application/json
```

**Body (JSON):**
```json
{
  "phone": "(11) 99999-1122",
  "address": "Rua A, 123 - Apto 45",
  "customerType": "vip"
}
```

**Expected Response (200):**
```json
{
  "affected": 1
}
```

---

### 6. DELETE - Deletar Cliente
**DELETE** `/customers/5`

**Expected Response (200):**
```json
{
  "affected": 1
}
```

---

## 🧪 Cenários de Teste

### Teste 1: Criar cliente VIP
```json
POST /customers
{
  "name": "Eduardo Santos",
  "email": "<EMAIL>",
  "phone": "(11) 99999-7777",
  "birthDate": "1980-12-10",
  "address": "Av. Faria Lima, 2000 - Itaim Bibi",
  "customerType": "vip",
  "active": true
}
```

### Teste 2: Criar cliente regular
```json
POST /customers
{
  "name": "Julia Costa",
  "email": "<EMAIL>",
  "phone": "(11) 99999-8888",
  "birthDate": "2000-03-15",
  "address": "Rua Augusta, 500",
  "customerType": "regular",
  "active": true
}
```

### Teste 3: Criar cliente jovem
```json
POST /customers
{
  "name": "Lucas Oliveira",
  "email": "<EMAIL>",
  "phone": "(11) 99999-9999",
  "birthDate": "2005-06-25",
  "address": "Rua dos Jovens, 789",
  "customerType": "regular",
  "active": true
}
```

### Teste 4: Tentar criar cliente com email duplicado
```json
POST /customers
{
  "name": "Outro Cliente",
  "email": "<EMAIL>",
  "phone": "(11) 99999-9999",
  "birthDate": "1985-01-01",
  "address": "Rua Teste, 123",
  "customerType": "regular",
  "active": true
}
```
**Expected:** Erro de email duplicado

### Teste 5: Desativar cliente
```json
PATCH /customers/2
{
  "active": false
}
```

### Teste 6: Promover cliente para VIP
```json
PATCH /customers/4
{
  "customerType": "vip"
}
```

### Teste 7: Buscar cliente por email inexistente
```
GET /customers/email/<EMAIL>
```
**Expected:** 404 ou null

### Teste 8: Atualizar endereço
```json
PATCH /customers/3
{
  "address": "Novo endereço, 456 - Novo bairro"
}
```

### Teste 9: Testar customerType inválido
```json
POST /customers
{
  "name": "Cliente Teste",
  "email": "<EMAIL>",
  "phone": "(11) 1111-1111",
  "birthDate": "1990-01-01",
  "address": "Rua Teste, 123",
  "customerType": "invalidType",
  "active": true
}
```
**Expected:** Erro de validação

---

## 🚨 **REGRAS DE NEGÓCIO - Testes de Exception**

### **CUSTOMER - 3 Regras Implementadas**

### **REGRA 1: Não permitir emails duplicados (CREATE)**

#### Teste A: Criar cliente com email duplicado (deve dar erro 409)
1. **Primeiro, crie um cliente:**
```json
POST /customers
{
  "name": "Cliente Original",
  "email": "<EMAIL>",
  "phone": "(11) 99999-1111",
  "birthDate": "1990-01-01",
  "address": "Rua Original, 123",
  "customerType": "regular",
  "active": true
}
```

2. **Depois, tente criar outro cliente com o mesmo email:**
```json
POST /customers
{
  "name": "Cliente Duplicado",
  "email": "<EMAIL>",
  "phone": "(11) 99999-2222",
  "birthDate": "1985-05-15",
  "address": "Rua Diferente, 456",
  "customerType": "premium",
  "active": true
}
```
**Expected Response (409 Conflict):**
```json
{
  "statusCode": 409,
  "message": "Email '<EMAIL>' já está em uso",
  "error": "Conflict"
}
```

### **REGRA 2: Não permitir downgrade de VIP para regular (UPDATE)**

#### Teste B: Downgrade VIP para regular (deve dar erro 400)
1. **Primeiro, crie um cliente VIP:**
```json
POST /customers
{
  "name": "Cliente VIP",
  "email": "<EMAIL>",
  "phone": "(11) 99999-8888",
  "birthDate": "1980-01-01",
  "address": "Rua VIP, 123",
  "customerType": "vip",
  "active": true
}
```

2. **Depois, tente fazer downgrade para regular:**
```json
PATCH /customers/[ID_DO_CLIENTE_VIP]
{
  "customerType": "regular"
}
```
**Expected Response (400 Bad Request):**
```json
{
  "statusCode": 400,
  "message": "Não é permitido fazer downgrade de cliente VIP para regular. Entre em contato com o suporte.",
  "error": "Bad Request"
}
```

#### Teste C: Upgrade de regular para VIP (deve funcionar)
```json
PATCH /customers/[ID_CLIENTE_REGULAR]
{
  "customerType": "vip"
}
```
**Expected:** Sucesso

#### Teste D: VIP para premium (deve funcionar)
```json
PATCH /customers/[ID_CLIENTE_VIP]
{
  "customerType": "premium"
}
```
**Expected:** Sucesso

#### Teste E: Premium para regular (deve funcionar)
```json
PATCH /customers/[ID_CLIENTE_PREMIUM]
{
  "customerType": "regular"
}
```
**Expected:** Sucesso (apenas VIP→regular é bloqueado)

### **REGRA 3: Não permitir deletar clientes com vendas (DELETE)**

#### Teste F: Deletar cliente com vendas (deve dar erro 400)
1. **Primeiro, crie uma venda para o cliente:**
```json
POST /sales
{
  "customerId": [ID_DO_CLIENTE],
  "storeId": 1,
  "paymentMethod": "pix",
  "seller": "Vendedor Teste",
  "items": [
    {
      "productId": 1,
      "productType": "phone",
      "quantity": 1,
      "unitPrice": 1000.00,
      "subtotal": 1000.00
    }
  ]
}
```

2. **Depois, tente deletar o cliente:**
```json
DELETE /customers/[ID_DO_CLIENTE]
```
**Expected Response (400 Bad Request):**
```json
{
  "statusCode": 400,
  "message": "Não é possível deletar cliente com vendas associadas. Cliente possui 1 venda(s).",
  "error": "Bad Request"
}
```

#### Teste G: Deletar cliente sem vendas (deve funcionar)
```json
DELETE /customers/[ID_CLIENTE_SEM_VENDAS]
```
**Expected:** Sucesso
