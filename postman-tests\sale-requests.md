# 💰 Sale (Vendas) - Requisi<PERSON><PERSON>es Postman

## Base URL
```
http://localhost:3000
```

## 📋 CRUD Operations

### 1. CREATE - Criar Nova Venda
**POST** `/sales`

**Headers:**
```
Content-Type: application/json
```

**Body (JSON):**
```json
{
  "customerId": 1,
  "storeId": 1,
  "paymentMethod": "pix",
  "seller": "João Vendedor",
  "items": [
    {
      "productId": 1,
      "productType": "phone",
      "quantity": 1,
      "unitPrice": 3999.99,
      "subtotal": 3999.99
    },
    {
      "productId": 1,
      "productType": "accessory",
      "quantity": 2,
      "unitPrice": 89.99,
      "subtotal": 179.98
    }
  ]
}
```

**Expected Response (201):**
```json
{
  "id": 1,
  "date": "2024-01-15T10:30:00.000Z",
  "customerId": 1,
  "storeId": 1,
  "totalValue": "4179.97",
  "paymentMethod": "pix",
  "status": "pending",
  "seller": "<PERSON>",
  "customer": {
    "id": 1,
    "name": "<PERSON>"
  },
  "store": {
    "id": 1,
    "name": "Loja Centro"
  },
  "items": [...]
}
```

---

### 2. READ ALL - Listar Todas as Vendas
**GET** `/sales`

**Expected Response (200):**
```json
[
  {
    "id": 1,
    "date": "2024-01-15T10:30:00.000Z",
    "customerId": 1,
    "storeId": 1,
    "totalValue": "4179.97",
    "paymentMethod": "pix",
    "status": "pending",
    "seller": "João Vendedor",
    "customer": {...},
    "store": {...},
    "items": [...]
  }
]
```

---

### 3. READ ALL BY STATUS - Filtrar por Status
**GET** `/sales?status=completed`

**Expected Response (200):**
```json
[
  {
    "id": 2,
    "status": "completed",
    "totalValue": "1299.99"
  }
]
```

---

### 4. READ ONE - Buscar Venda por ID
**GET** `/sales/1`

**Expected Response (200):**
```json
{
  "id": 1,
  "date": "2024-01-15T10:30:00.000Z",
  "customerId": 1,
  "storeId": 1,
  "totalValue": "4179.97",
  "paymentMethod": "pix",
  "status": "pending",
  "seller": "João Vendedor",
  "customer": {
    "id": 1,
    "name": "Ana Silva",
    "email": "<EMAIL>"
  },
  "store": {
    "id": 1,
    "name": "Loja Centro"
  },
  "items": [
    {
      "id": 1,
      "productId": 1,
      "productType": "phone",
      "quantity": 1,
      "unitPrice": "3999.99",
      "subtotal": "3999.99"
    }
  ]
}
```

---

### 5. READ BY CUSTOMER - Vendas por Cliente
**GET** `/sales/customer/1`

**Expected Response (200):**
```json
[
  {
    "id": 1,
    "customerId": 1,
    "totalValue": "4179.97",
    "customer": {
      "id": 1,
      "name": "Ana Silva"
    }
  }
]
```

---

### 6. READ BY STORE - Vendas por Loja
**GET** `/sales/store/1`

**Expected Response (200):**
```json
[
  {
    "id": 1,
    "storeId": 1,
    "totalValue": "4179.97",
    "store": {
      "id": 1,
      "name": "Loja Centro"
    }
  }
]
```

---

### 7. UPDATE - Atualizar Venda
**PATCH** `/sales/1`

**Headers:**
```
Content-Type: application/json
```

**Body (JSON):**
```json
{
  "seller": "Maria Vendedora",
  "paymentMethod": "credit"
}
```

**Expected Response (200):**
```json
{
  "affected": 1
}
```

---

### 8. UPDATE STATUS - Atualizar Status da Venda
**PATCH** `/sales/1/status`

**Headers:**
```
Content-Type: application/json
```

**Body (JSON):**
```json
{
  "status": "completed"
}
```

**Expected Response (200):**
```json
{
  "affected": 1
}
```

---

### 9. DELETE - Deletar Venda
**DELETE** `/sales/1`

**Expected Response (200):**
```json
{
  "affected": 1
}
```

---

## 🧪 Cenários de Teste

### Teste 1: Venda só de acessórios
```json
POST /sales
{
  "customerId": 2,
  "storeId": 2,
  "paymentMethod": "debit",
  "seller": "Carlos Vendedor",
  "items": [
    {
      "productId": 2,
      "productType": "accessory",
      "quantity": 1,
      "unitPrice": 149.99,
      "subtotal": 149.99
    },
    {
      "productId": 3,
      "productType": "accessory",
      "quantity": 2,
      "unitPrice": 39.99,
      "subtotal": 79.98
    }
  ]
}
```

### Teste 2: Venda grande (múltiplos itens)
```json
POST /sales
{
  "customerId": 3,
  "storeId": 1,
  "paymentMethod": "credit",
  "seller": "Ana Vendedora",
  "items": [
    {
      "productId": 2,
      "productType": "phone",
      "quantity": 2,
      "unitPrice": 7999.99,
      "subtotal": 15999.98
    },
    {
      "productId": 4,
      "productType": "accessory",
      "quantity": 3,
      "unitPrice": 299.99,
      "subtotal": 899.97
    }
  ]
}
```

### Teste 3: Venda simples (só um celular)
```json
POST /sales
{
  "customerId": 4,
  "storeId": 3,
  "paymentMethod": "pix",
  "seller": "Pedro Vendedor",
  "items": [
    {
      "productId": 3,
      "productType": "phone",
      "quantity": 1,
      "unitPrice": 1299.99,
      "subtotal": 1299.99
    }
  ]
}
```

### Teste 4: Filtrar vendas pendentes
```
GET /sales?status=pending
```

### Teste 5: Filtrar vendas canceladas
```
GET /sales?status=canceled
```

### Teste 6: Cancelar venda
```json
PATCH /sales/2/status
{
  "status": "canceled"
}
```

### Teste 7: Completar venda
```json
PATCH /sales/3/status
{
  "status": "completed"
}
```
