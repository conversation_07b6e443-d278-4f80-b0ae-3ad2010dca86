# 🏪 Store (Lojas) - Requisições Postman

## Base URL
```
http://localhost:3000
```

## 📋 CRUD Operations

### 1. CREATE - Criar <PERSON>ja
**POST** `/stores`

**Headers:**
```
Content-Type: application/json
```

**Body (JSON):**
```json
{
  "name": "Loja Barra",
  "address": "Av. das Américas, 4666 - Barra da Tijuca",
  "city": "Rio de Janeiro",
  "state": "RJ",
  "phone": "(21) 3333-4444",
  "manager": "<PERSON>",
  "isHeadquarters": false,
  "status": "active"
}
```

**Expected Response (201):**
```json
{
  "id": 4,
  "name": "Loja Barra",
  "address": "Av. das Américas, 4666 - Barra da Tijuca",
  "city": "Rio de Janeiro",
  "state": "RJ",
  "phone": "(21) 3333-4444",
  "manager": "<PERSON>",
  "isHeadquarters": false,
  "status": "active"
}
```

---

### 2. READ ALL - <PERSON><PERSON> as <PERSON><PERSON>
**GET** `/stores`

**Expected Response (200):**
```json
[
  {
    "id": 1,
    "name": "Loja Centro",
    "address": "Rua das Flores, 123",
    "city": "São Paulo",
    "state": "SP",
    "phone": "(11) 1234-5678",
    "manager": "João Silva",
    "isHeadquarters": true,
    "status": "active",
    "sales": [...]
  }
]
```

---

### 3. READ ONE - Buscar Loja por ID
**GET** `/stores/1`

**Expected Response (200):**
```json
{
  "id": 1,
  "name": "Loja Centro",
  "address": "Rua das Flores, 123",
  "city": "São Paulo",
  "state": "SP",
  "phone": "(11) 1234-5678",
  "manager": "João Silva",
  "isHeadquarters": true,
  "status": "active",
  "sales": [...]
}
```

---

### 4. READ ACTIVE - Listar Lojas Ativas
**GET** `/stores/active/list`

**Expected Response (200):**
```json
[
  {
    "id": 1,
    "name": "Loja Centro",
    "status": "active"
  },
  {
    "id": 2,
    "name": "Loja Shopping",
    "status": "active"
  }
]
```

---

### 5. UPDATE - Atualizar Loja
**PATCH** `/stores/1`

**Headers:**
```
Content-Type: application/json
```

**Body (JSON):**
```json
{
  "phone": "(11) 1234-9999",
  "manager": "João Silva Santos"
}
```

**Expected Response (200):**
```json
{
  "affected": 1
}
```

---

### 6. DELETE - Deletar Loja
**DELETE** `/stores/4`

**Expected Response (200):**
```json
{
  "affected": 1
}
```

---

## 🧪 Cenários de Teste

### Teste 1: Criar loja em manutenção
```json
POST /stores
{
  "name": "Loja Campinas",
  "address": "Rua Barão de Jaguara, 1000",
  "city": "Campinas",
  "state": "SP",
  "phone": "(19) 3333-5555",
  "manager": "Ana Costa",
  "isHeadquarters": false,
  "status": "underMaintenance"
}
```

### Teste 2: Criar loja inativa
```json
POST /stores
{
  "name": "Loja Brasília",
  "address": "SCS Quadra 1, Bloco A",
  "city": "Brasília",
  "state": "DF",
  "phone": "(61) 3333-6666",
  "manager": "Pedro Santos",
  "isHeadquarters": false,
  "status": "inactive"
}
```

### Teste 3: Criar segunda matriz (teste de regra de negócio)
```json
POST /stores
{
  "name": "Nova Matriz",
  "address": "Av. Paulista, 1000",
  "city": "São Paulo",
  "state": "SP",
  "phone": "(11) 5555-6666",
  "manager": "Roberto Lima",
  "isHeadquarters": true,
  "status": "active"
}
```

### Teste 4: Alterar status da loja
```json
PATCH /stores/2
{
  "status": "inactive"
}
```

### Teste 5: Alterar gerente
```json
PATCH /stores/3
{
  "manager": "Novo Gerente Silva"
}
```

### Teste 6: Buscar apenas lojas ativas
```
GET /stores/active/list
```

### Teste 7: Testar status inválido
```json
POST /stores
{
  "name": "Loja Teste Status",
  "address": "Rua Teste, 123",
  "city": "São Paulo",
  "state": "SP",
  "phone": "(11) 1111-1111",
  "manager": "Teste Manager",
  "isHeadquarters": false,
  "status": "invalidStatus"
}
```
**Expected:** Erro de validação

---

## 🚨 **REGRAS DE NEGÓCIO - Testes de Exception**

### **REGRA 5: Não retornar lojas inativas (método específico)**

#### Teste A: Buscar apenas lojas ativas quando não há (deve dar erro 400)

**Novo Endpoint:** `GET /stores/active/only`

1. **Primeiro, desative todas as lojas:**
```json
PATCH /stores/1
{
  "status": "inactive"
}
```
```json
PATCH /stores/2
{
  "status": "underMaintenance"
}
```
*(Repita para todas as lojas)*

2. **Depois, tente buscar apenas lojas ativas:**
```
GET /stores/active/only
```
**Expected Response (400 Bad Request):**
```json
{
  "statusCode": 400,
  "message": "Nenhuma loja ativa disponível no momento",
  "error": "Bad Request"
}
```

#### Teste B: Buscar lojas ativas quando há (deve funcionar)
1. **Primeiro, ative uma loja:**
```json
PATCH /stores/1
{
  "status": "active"
}
```

2. **Depois, busque lojas ativas:**
```
GET /stores/active/only
```
**Expected:** Lista com apenas lojas ativas

#### Teste C: Comparar endpoints diferentes
- `GET /stores/active/list` - Retorna array vazio se não houver lojas ativas
- `GET /stores/active/only` - Retorna erro 400 se não houver lojas ativas

#### Teste D: Cenário misto de status
1. **Configure lojas com status diferentes:**
```json
PATCH /stores/1
{
  "status": "active"
}
```
```json
PATCH /stores/2
{
  "status": "inactive"
}
```
```json
PATCH /stores/3
{
  "status": "underMaintenance"
}
```

2. **Teste os endpoints:**
```
GET /stores/active/list
```
**Expected:** Apenas loja 1

```
GET /stores/active/only
```
**Expected:** Apenas loja 1 (sem erro)

#### Teste E: Todas as lojas inativas
1. **Desative todas:**
```json
PATCH /stores/1
{
  "status": "inactive"
}
```

2. **Teste diferença:**
```
GET /stores/active/list
```
**Expected:** `[]` (array vazio)

```
GET /stores/active/only
```
**Expected:** Erro 400
