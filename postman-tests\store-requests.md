# 🏪 Store (Lojas) - Requisições Postman

## Base URL
```
http://localhost:3000
```

## 📋 CRUD Operations

### 1. CREATE - Criar <PERSON>ja
**POST** `/stores`

**Headers:**
```
Content-Type: application/json
```

**Body (JSON):**
```json
{
  "name": "Loja Barra",
  "address": "Av. das Américas, 4666 - Barra da Tijuca",
  "city": "Rio de Janeiro",
  "state": "RJ",
  "phone": "(21) 3333-4444",
  "manager": "<PERSON>",
  "isHeadquarters": false,
  "status": "active"
}
```

**Expected Response (201):**
```json
{
  "id": 4,
  "name": "Loja Barra",
  "address": "Av. das Américas, 4666 - Barra da Tijuca",
  "city": "Rio de Janeiro",
  "state": "RJ",
  "phone": "(21) 3333-4444",
  "manager": "<PERSON>",
  "isHeadquarters": false,
  "status": "active"
}
```

---

### 2. READ ALL - <PERSON><PERSON> as <PERSON><PERSON>
**GET** `/stores`

**Expected Response (200):**
```json
[
  {
    "id": 1,
    "name": "Loja Centro",
    "address": "Rua das Flores, 123",
    "city": "São Paulo",
    "state": "SP",
    "phone": "(11) 1234-5678",
    "manager": "João Silva",
    "isHeadquarters": true,
    "status": "active",
    "sales": [...]
  }
]
```

---

### 3. READ ONE - Buscar Loja por ID
**GET** `/stores/1`

**Expected Response (200):**
```json
{
  "id": 1,
  "name": "Loja Centro",
  "address": "Rua das Flores, 123",
  "city": "São Paulo",
  "state": "SP",
  "phone": "(11) 1234-5678",
  "manager": "João Silva",
  "isHeadquarters": true,
  "status": "active",
  "sales": [...]
}
```

---

### 4. READ ACTIVE - Listar Lojas Ativas
**GET** `/stores/active/list`

**Expected Response (200):**
```json
[
  {
    "id": 1,
    "name": "Loja Centro",
    "status": "active"
  },
  {
    "id": 2,
    "name": "Loja Shopping",
    "status": "active"
  }
]
```

---

### 5. UPDATE - Atualizar Loja
**PATCH** `/stores/1`

**Headers:**
```
Content-Type: application/json
```

**Body (JSON):**
```json
{
  "phone": "(11) 1234-9999",
  "manager": "João Silva Santos"
}
```

**Expected Response (200):**
```json
{
  "affected": 1
}
```

---

### 6. DELETE - Deletar Loja
**DELETE** `/stores/4`

**Expected Response (200):**
```json
{
  "affected": 1
}
```

---

## 🧪 Cenários de Teste

### Teste 1: Criar loja em manutenção
```json
POST /stores
{
  "name": "Loja Campinas",
  "address": "Rua Barão de Jaguara, 1000",
  "city": "Campinas",
  "state": "SP",
  "phone": "(19) 3333-5555",
  "manager": "Ana Costa",
  "isHeadquarters": false,
  "status": "underMaintenance"
}
```

### Teste 2: Criar loja inativa
```json
POST /stores
{
  "name": "Loja Brasília",
  "address": "SCS Quadra 1, Bloco A",
  "city": "Brasília",
  "state": "DF",
  "phone": "(61) 3333-6666",
  "manager": "Pedro Santos",
  "isHeadquarters": false,
  "status": "inactive"
}
```

### Teste 3: Criar segunda matriz (teste de regra de negócio)
```json
POST /stores
{
  "name": "Nova Matriz",
  "address": "Av. Paulista, 1000",
  "city": "São Paulo",
  "state": "SP",
  "phone": "(11) 5555-6666",
  "manager": "Roberto Lima",
  "isHeadquarters": true,
  "status": "active"
}
```

### Teste 4: Alterar status da loja
```json
PATCH /stores/2
{
  "status": "inactive"
}
```

### Teste 5: Alterar gerente
```json
PATCH /stores/3
{
  "manager": "Novo Gerente Silva"
}
```

### Teste 6: Buscar apenas lojas ativas
```
GET /stores/active/list
```

### Teste 7: Testar status inválido
```json
POST /stores
{
  "name": "Loja Teste Status",
  "address": "Rua Teste, 123",
  "city": "São Paulo",
  "state": "SP",
  "phone": "(11) 1111-1111",
  "manager": "Teste Manager",
  "isHeadquarters": false,
  "status": "invalidStatus"
}
```
**Expected:** Erro de validação
