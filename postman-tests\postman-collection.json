{"info": {"name": "Revenda API", "description": "Collection completa para testar a API de Revenda de Celulares", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "baseUrl", "value": "http://localhost:3000", "type": "string"}], "item": [{"name": "1. <PERSON><PERSON>", "item": [{"name": "Create Brand", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"OnePlus\",\n  \"country\": \"China\"\n}"}, "url": {"raw": "{{baseUrl}}/brands", "host": ["{{baseUrl}}"], "path": ["brands"]}}}, {"name": "Get All Brands", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/brands", "host": ["{{baseUrl}}"], "path": ["brands"]}}}, {"name": "Get Brand by ID", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/brands/1", "host": ["{{baseUrl}}"], "path": ["brands", "1"]}}}, {"name": "Update Brand", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Samsung Electronics\"\n}"}, "url": {"raw": "{{baseUrl}}/brands/1", "host": ["{{baseUrl}}"], "path": ["brands", "1"]}}}, {"name": "Delete Brand", "request": {"method": "DELETE", "url": {"raw": "{{baseUrl}}/brands/6", "host": ["{{baseUrl}}"], "path": ["brands", "6"]}}}]}, {"name": "2. Phones", "item": [{"name": "Create Phone", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"model\": \"Galaxy S24 Ultra\",\n  \"image\": \"https://example.com/galaxy-s24-ultra.jpg\",\n  \"releaseDate\": \"2024-01-15\",\n  \"price\": 8999.99,\n  \"category\": \"Smartphone\",\n  \"brandId\": 1\n}"}, "url": {"raw": "{{baseUrl}}/phones", "host": ["{{baseUrl}}"], "path": ["phones"]}}}, {"name": "Get All Phones", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/phones", "host": ["{{baseUrl}}"], "path": ["phones"]}}}, {"name": "Get Phone by ID", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/phones/1", "host": ["{{baseUrl}}"], "path": ["phones", "1"]}}}, {"name": "Get Phones by Brand", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/phones/brand/1", "host": ["{{baseUrl}}"], "path": ["phones", "brand", "1"]}}}]}, {"name": "3. Accessories", "item": [{"name": "Create Accessory", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"AirPods Pro 2\",\n  \"description\": \"Fones sem fio com cancelamento ativo de ruído\",\n  \"price\": 1899.99,\n  \"category\": \"Fone\",\n  \"image\": \"https://example.com/airpods-pro-2.jpg\",\n  \"stock\": 15\n}"}, "url": {"raw": "{{baseUrl}}/accessories", "host": ["{{baseUrl}}"], "path": ["accessories"]}}}, {"name": "Get All Accessories", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/accessories", "host": ["{{baseUrl}}"], "path": ["accessories"]}}}, {"name": "Get Accessories by Category", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/accessories/category/Fone", "host": ["{{baseUrl}}"], "path": ["accessories", "category", "Fone"]}}}, {"name": "Get Accessories in Stock", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/accessories/stock/available", "host": ["{{baseUrl}}"], "path": ["accessories", "stock", "available"]}}}, {"name": "Update Stock", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"quantity\": 10\n}"}, "url": {"raw": "{{baseUrl}}/accessories/1/stock", "host": ["{{baseUrl}}"], "path": ["accessories", "1", "stock"]}}}]}, {"name": "4. Stores", "item": [{"name": "Create Store", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"<PERSON>ja Barra\",\n  \"address\": \"Av. das Américas, 4666\",\n  \"city\": \"Rio de Janeiro\",\n  \"state\": \"RJ\",\n  \"phone\": \"(21) 3333-4444\",\n  \"manager\": \"<PERSON>\",\n  \"isHeadquarters\": false,\n  \"status\": \"active\"\n}"}, "url": {"raw": "{{baseUrl}}/stores", "host": ["{{baseUrl}}"], "path": ["stores"]}}}, {"name": "Get All Stores", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/stores", "host": ["{{baseUrl}}"], "path": ["stores"]}}}, {"name": "Get Active Stores", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/stores/active/list", "host": ["{{baseUrl}}"], "path": ["stores", "active", "list"]}}}]}, {"name": "5. Customers", "item": [{"name": "Create Customer", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"<PERSON>\",\n  \"email\": \"<EMAIL>\",\n  \"phone\": \"(11) 99999-5555\",\n  \"birthDate\": \"1995-07-20\",\n  \"address\": \"<PERSON><PERSON> das Palmeiras, 456\",\n  \"customerType\": \"premium\",\n  \"active\": true\n}"}, "url": {"raw": "{{baseUrl}}/customers", "host": ["{{baseUrl}}"], "path": ["customers"]}}}, {"name": "Get All Customers", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/customers", "host": ["{{baseUrl}}"], "path": ["customers"]}}}, {"name": "Get Customer by Email", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/customers/email/<EMAIL>", "host": ["{{baseUrl}}"], "path": ["customers", "email", "<EMAIL>"]}}}]}, {"name": "6. Sales", "item": [{"name": "Create Sale", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"customerId\": 1,\n  \"storeId\": 1,\n  \"paymentMethod\": \"pix\",\n  \"seller\": \"<PERSON>endedor\",\n  \"items\": [\n    {\n      \"productId\": 1,\n      \"productType\": \"phone\",\n      \"quantity\": 1,\n      \"unitPrice\": 3999.99,\n      \"subtotal\": 3999.99\n    },\n    {\n      \"productId\": 1,\n      \"productType\": \"accessory\",\n      \"quantity\": 2,\n      \"unitPrice\": 89.99,\n      \"subtotal\": 179.98\n    }\n  ]\n}"}, "url": {"raw": "{{baseUrl}}/sales", "host": ["{{baseUrl}}"], "path": ["sales"]}}}, {"name": "Get All Sales", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/sales", "host": ["{{baseUrl}}"], "path": ["sales"]}}}, {"name": "Get Sales by Status", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/sales?status=pending", "host": ["{{baseUrl}}"], "path": ["sales"], "query": [{"key": "status", "value": "pending"}]}}}, {"name": "Get Sales by Customer", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/sales/customer/1", "host": ["{{baseUrl}}"], "path": ["sales", "customer", "1"]}}}, {"name": "Update Sale Status", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"status\": \"completed\"\n}"}, "url": {"raw": "{{baseUrl}}/sales/1/status", "host": ["{{baseUrl}}"], "path": ["sales", "1", "status"]}}}]}]}