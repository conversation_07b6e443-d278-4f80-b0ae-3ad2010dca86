# 🧪 Testes Postman - Revenda API

## 📁 Estrutura dos Arquivos

```
postman-tests/
├── README.md                    # Este arquivo
├── ALL-REQUESTS.md             # Visão geral de todas as requisições
├── postman-collection.json     # Collection para importar no Postman
├── brand-requests.md           # Testes específicos para Brands
├── phone-requests.md           # Testes específicos para Phones
├── accessory-requests.md       # Testes específicos para Accessories
├── store-requests.md           # Testes específicos para Stores
├── customer-requests.md        # Testes específicos para Customers
└── sale-requests.md            # Testes específicos para Sales
```

## 🚀 Como Usar

### Opção 1: Importar Collection no Postman (Recomendado)
1. <PERSON><PERSON> o Postman
2. Clique em **"Import"**
3. Selecione o arquivo `postman-collection.json`
4. A collection **"Revenda API"** será criada
5. Configure a variável `baseUrl` para `http://localhost:3000`

### Opção 2: Criar Requisições Manualmente
1. Consulte os arquivos `.md` específicos de cada entidade
2. <PERSON>pie e cole as requisições no Postman
3. Configure o Base URL como `http://localhost:3000`

## 📋 Sequência de Testes Recomendada

### 🎯 1. Preparação (Entidades Base)
Execute nesta ordem para criar dados base:

1. **Brands** - Criar marcas
   ```
   POST /brands
   ```

2. **Stores** - Criar lojas
   ```
   POST /stores
   ```

3. **Customers** - Criar clientes
   ```
   POST /customers
   ```

### 🎯 2. Produtos (Dependem das marcas)
4. **Phones** - Criar celulares
   ```
   POST /phones
   ```

5. **Accessories** - Criar acessórios
   ```
   POST /accessories
   ```

### 🎯 3. Vendas (Dependem de tudo)
6. **Sales** - Criar vendas
   ```
   POST /sales
   ```

## 🔧 Configuração do Ambiente

### Variáveis do Postman
Configure estas variáveis no seu ambiente:

| Variável | Valor | Descrição |
|----------|-------|-----------|
| `baseUrl` | `http://localhost:3000` | URL base da API |

### Headers Padrão
Para requisições POST/PATCH, sempre use:
```
Content-Type: application/json
```

## 📊 Dados de Exemplo

### Marcas para Teste
```json
{ "name": "Samsung", "country": "Coreia do Sul" }
{ "name": "Apple", "country": "Estados Unidos" }
{ "name": "Xiaomi", "country": "China" }
```

### Lojas para Teste
```json
{ "name": "Loja Centro", "address": "Rua A, 123", "city": "São Paulo", "state": "SP", "phone": "(11) 1111-1111", "manager": "João Silva", "isHeadquarters": true, "status": "active" }
```

### Clientes para Teste
```json
{ "name": "Ana Silva", "email": "<EMAIL>", "phone": "(11) 99999-1111", "birthDate": "1990-01-01", "address": "Rua A, 123", "customerType": "premium", "active": true }
```

## 🧪 Cenários de Teste

### ✅ Testes Básicos (CRUD)
- [ ] Criar entidade
- [ ] Listar todas
- [ ] Buscar por ID
- [ ] Atualizar entidade
- [ ] Deletar entidade

### ✅ Testes de Relacionamento
- [ ] Buscar phones por brand
- [ ] Buscar accessories por categoria
- [ ] Buscar sales por customer
- [ ] Buscar sales por store

### ✅ Testes de Validação
- [ ] Criar com dados inválidos
- [ ] Buscar ID inexistente
- [ ] Atualizar entidade inexistente
- [ ] Testar campos obrigatórios

### ✅ Testes de Negócio
- [ ] Controle de estoque de accessories
- [ ] Status de vendas (pending → completed → canceled)
- [ ] Tipos de cliente (regular → premium → vip)
- [ ] Status de lojas (active, inactive, underMaintenance)

## 🔍 Verificações Importantes

### Antes de Testar
- [ ] API está rodando em `http://localhost:3000`
- [ ] Banco de dados está configurado
- [ ] Migrations foram executadas
- [ ] Seeds foram executadas (opcional)

### Durante os Testes
- [ ] Verificar códigos de status HTTP
- [ ] Verificar estrutura das respostas
- [ ] Verificar relacionamentos
- [ ] Verificar validações

### Códigos de Status Esperados
- **200**: GET, PATCH, DELETE bem-sucedidos
- **201**: POST bem-sucedido
- **400**: Dados inválidos
- **404**: Recurso não encontrado
- **500**: Erro interno do servidor

## 🐛 Troubleshooting

### Problemas Comuns

#### 1. Erro de Conexão
```
Error: connect ECONNREFUSED 127.0.0.1:3000
```
**Solução**: Verificar se a API está rodando

#### 2. Erro 404 em todas as rotas
```
Cannot GET /brands
```
**Solução**: Verificar se o servidor está rodando na porta correta

#### 3. Erro de Foreign Key
```
foreign key constraint fails
```
**Solução**: Criar as entidades base primeiro (brands, stores, customers)

#### 4. Erro de Validação
```
ValidationError: ...
```
**Solução**: Verificar se todos os campos obrigatórios estão preenchidos

## 📞 Suporte

Se encontrar problemas:
1. Verifique os logs do servidor
2. Confirme se o banco está rodando
3. Verifique se as migrations foram executadas
4. Consulte a documentação da API
