# 🔌 Accessory (Acessórios) - Requisições Postman

## Base URL
```
http://localhost:3000
```

## 📋 CRUD Operations

### 1. CREATE - Criar Novo Acessório
**POST** `/accessories`

**Headers:**
```
Content-Type: application/json
```

**Body (JSON):**
```json
{
  "name": "AirPods Pro 2",
  "description": "Fones sem fio com cancelamento ativo de ruído",
  "price": 1899.99,
  "category": "Fone",
  "image": "https://example.com/airpods-pro-2.jpg",
  "stock": 15
}
```

**Expected Response (201):**
```json
{
  "id": 6,
  "name": "AirPods Pro 2",
  "description": "Fones sem fio com cancelamento ativo de ruído",
  "price": "1899.99",
  "category": "Fone",
  "image": "https://example.com/airpods-pro-2.jpg",
  "stock": 15
}
```

---

### 2. READ ALL - Listar Todos os Acessórios
**GET** `/accessories`

**Expected Response (200):**
```json
[
  {
    "id": 1,
    "name": "Capa Protetora Premium",
    "description": "Capa resistente a quedas com proteção militar",
    "price": "89.99",
    "category": "Capa",
    "image": "https://example.com/capa-premium.jpg",
    "stock": 50,
    "compatiblePhones": [...]
  }
]
```

---

### 3. READ ONE - Buscar Acessório por ID
**GET** `/accessories/1`

**Expected Response (200):**
```json
{
  "id": 1,
  "name": "Capa Protetora Premium",
  "description": "Capa resistente a quedas com proteção militar",
  "price": "89.99",
  "category": "Capa",
  "image": "https://example.com/capa-premium.jpg",
  "stock": 50,
  "compatiblePhones": [
    {
      "id": 1,
      "model": "Galaxy S23"
    }
  ]
}
```

---

### 4. READ BY CATEGORY - Buscar por Categoria
**GET** `/accessories/category/Fone`

**Expected Response (200):**
```json
[
  {
    "id": 4,
    "name": "Fone Bluetooth Premium",
    "category": "Fone",
    "price": "299.99",
    "stock": 25
  }
]
```

---

### 5. READ IN STOCK - Acessórios em Estoque
**GET** `/accessories/stock/available`

**Expected Response (200):**
```json
[
  {
    "id": 1,
    "name": "Capa Protetora Premium",
    "stock": 50
  },
  {
    "id": 2,
    "name": "Carregador Rápido 65W",
    "stock": 30
  }
]
```

---

### 6. UPDATE - Atualizar Acessório
**PATCH** `/accessories/1`

**Headers:**
```
Content-Type: application/json
```

**Body (JSON):**
```json
{
  "price": 79.99,
  "stock": 45
}
```

**Expected Response (200):**
```json
{
  "affected": 1
}
```

---

### 7. UPDATE STOCK - Atualizar Estoque
**PATCH** `/accessories/1/stock`

**Headers:**
```
Content-Type: application/json
```

**Body (JSON):**
```json
{
  "quantity": 10
}
```

**Expected Response (200):**
```json
{
  "affected": 1
}
```

---

### 8. DELETE - Deletar Acessório
**DELETE** `/accessories/6`

**Expected Response (200):**
```json
{
  "affected": 1
}
```

---

## 🧪 Cenários de Teste

### Teste 1: Criar carregador wireless
```json
POST /accessories
{
  "name": "Carregador Wireless 15W",
  "description": "Carregamento sem fio rápido",
  "price": 199.99,
  "category": "Carregador",
  "image": "https://example.com/wireless-charger.jpg",
  "stock": 20
}
```

### Teste 2: Criar power bank
```json
POST /accessories
{
  "name": "Power Bank 20000mAh",
  "description": "Bateria portátil de alta capacidade",
  "price": 149.99,
  "category": "Bateria",
  "image": "https://example.com/power-bank.jpg",
  "stock": 35
}
```

### Teste 3: Buscar capas
```
GET /accessories/category/Capa
```

### Teste 4: Buscar carregadores
```
GET /accessories/category/Carregador
```

### Teste 5: Adicionar estoque
```json
PATCH /accessories/2/stock
{
  "quantity": 5
}
```

### Teste 6: Remover estoque
```json
PATCH /accessories/2/stock
{
  "quantity": -3
}
```

### Teste 7: Verificar produtos em estoque
```
GET /accessories/stock/available
```

---

## 🚨 **REGRAS DE NEGÓCIO - Testes de Exception**

### **REGRA 4: Não retornar acessórios sem estoque (método específico)**

#### Teste A: Buscar apenas acessórios em estoque quando não há (deve dar erro 400)

**Novo Endpoint:** `GET /accessories/stock/only-available`

1. **Primeiro, zere o estoque de todos os acessórios:**
```json
PATCH /accessories/1/stock
{
  "quantity": -50
}
```
```json
PATCH /accessories/2/stock
{
  "quantity": -30
}
```
*(Repita para todos os acessórios)*

2. **Depois, tente buscar apenas acessórios em estoque:**
```
GET /accessories/stock/only-available
```
**Expected Response (400 Bad Request):**
```json
{
  "statusCode": 400,
  "message": "Nenhum acessório disponível em estoque no momento",
  "error": "Bad Request"
}
```

#### Teste B: Buscar acessórios em estoque quando há produtos (deve funcionar)
1. **Primeiro, adicione estoque a um acessório:**
```json
PATCH /accessories/1/stock
{
  "quantity": 10
}
```

2. **Depois, busque acessórios em estoque:**
```
GET /accessories/stock/only-available
```
**Expected:** Lista com apenas acessórios que têm estoque > 0

#### Teste C: Comparar endpoints diferentes
- `GET /accessories/stock/available` - Retorna todos com estoque > 0 (sem erro)
- `GET /accessories/stock/only-available` - Retorna erro se não houver estoque

#### Teste D: Cenário de estoque zerado
1. **Crie um acessório com estoque 0:**
```json
POST /accessories
{
  "name": "Produto Sem Estoque",
  "description": "Teste de produto sem estoque",
  "price": 99.99,
  "category": "Teste",
  "image": "https://example.com/sem-estoque.jpg",
  "stock": 0
}
```

2. **Teste os diferentes endpoints:**
```
GET /accessories/stock/available
```
**Expected:** Array vazio `[]`

```
GET /accessories/stock/only-available
```
**Expected:** Erro 400
