# 📱 Phone (Celulares) - Requisi<PERSON><PERSON>es Postman

## Base URL
```
http://localhost:3000
```

## 📋 CRUD Operations

### 1. CREATE - <PERSON>riar <PERSON>
**POST** `/phones`

**Headers:**
```
Content-Type: application/json
```

**Body (JSON):**
```json
{
  "model": "Galaxy S24 Ultra",
  "image": "https://example.com/galaxy-s24-ultra.jpg",
  "releaseDate": "2024-01-15",
  "price": 8999.99,
  "category": "Smartphone",
  "brandId": 1
}
```

**Expected Response (201):**
```json
{
  "id": 6,
  "model": "Galaxy S24 Ultra",
  "image": "https://example.com/galaxy-s24-ultra.jpg",
  "releaseDate": "2024-01-15",
  "price": "8999.99",
  "category": "Smartphone",
  "brandId": 1
}
```

---

### 2. READ ALL - Listar Todos os Celulares
**GET** `/phones`

**Expected Response (200):**
```json
[
  {
    "id": 1,
    "model": "Galaxy S23",
    "image": "https://example.com/galaxy-s23.jpg",
    "releaseDate": "2023-02-01",
    "price": "3999.99",
    "category": "Smartphone",
    "brandId": 1,
    "brand": {
      "id": 1,
      "name": "Samsung",
      "country": "Coreia do Sul"
    },
    "accessories": [...]
  }
]
```

---

### 3. READ ONE - Buscar Celular por ID
**GET** `/phones/1`

**Expected Response (200):**
```json
{
  "id": 1,
  "model": "Galaxy S23",
  "image": "https://example.com/galaxy-s23.jpg",
  "releaseDate": "2023-02-01",
  "price": "3999.99",
  "category": "Smartphone",
  "brandId": 1,
  "brand": {
    "id": 1,
    "name": "Samsung",
    "country": "Coreia do Sul"
  },
  "accessories": [...]
}
```

---

### 4. READ BY BRAND - Buscar Celulares por Marca
**GET** `/phones/brand/1`

**Expected Response (200):**
```json
[
  {
    "id": 1,
    "model": "Galaxy S23",
    "brandId": 1,
    "brand": {
      "id": 1,
      "name": "Samsung"
    }
  }
]
```

---

### 5. UPDATE - Atualizar Celular
**PATCH** `/phones/1`

**Headers:**
```
Content-Type: application/json
```

**Body (JSON):**
```json
{
  "price": 3499.99,
  "image": "https://example.com/galaxy-s23-new.jpg"
}
```

**Expected Response (200):**
```json
{
  "affected": 1
}
```

---

### 6. DELETE - Deletar Celular
**DELETE** `/phones/6`

**Expected Response (200):**
```json
{
  "affected": 1
}
```

---

## 🧪 Cenários de Teste

### Teste 1: Criar celular iPhone
```json
POST /phones
{
  "model": "iPhone 16 Pro Max",
  "image": "https://example.com/iphone-16-pro-max.jpg",
  "releaseDate": "2024-09-15",
  "price": 9999.99,
  "category": "Smartphone",
  "brandId": 2
}
```

### Teste 2: Criar celular Xiaomi
```json
POST /phones
{
  "model": "Redmi Note 13 Pro",
  "image": "https://example.com/redmi-note-13-pro.jpg",
  "releaseDate": "2024-03-01",
  "price": 1799.99,
  "category": "Smartphone",
  "brandId": 3
}
```

### Teste 3: Buscar celulares da Apple
```
GET /phones/brand/2
```

### Teste 4: Buscar celulares da Samsung
```
GET /phones/brand/1
```

### Teste 5: Atualizar preço
```json
PATCH /phones/2
{
  "price": 7499.99
}
```

### Teste 6: Tentar criar celular com marca inexistente
```json
POST /phones
{
  "model": "Celular Teste",
  "image": "https://example.com/teste.jpg",
  "releaseDate": "2024-01-01",
  "price": 1000.00,
  "category": "Smartphone",
  "brandId": 999
}
```
**Expected:** Erro de foreign key
