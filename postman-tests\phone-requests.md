# 📱 Phone (Celulares) - Requisi<PERSON><PERSON>es Postman

## Base URL
```
http://localhost:3000
```

## 📋 CRUD Operations

### 1. CREATE - <PERSON>riar <PERSON>
**POST** `/phones`

**Headers:**
```
Content-Type: application/json
```

**Body (JSON):**
```json
{
  "model": "Galaxy S24 Ultra",
  "image": "https://example.com/galaxy-s24-ultra.jpg",
  "releaseDate": "2024-01-15",
  "price": 8999.99,
  "category": "Smartphone",
  "brandId": 1
}
```

**Expected Response (201):**
```json
{
  "id": 6,
  "model": "Galaxy S24 Ultra",
  "image": "https://example.com/galaxy-s24-ultra.jpg",
  "releaseDate": "2024-01-15",
  "price": "8999.99",
  "category": "Smartphone",
  "brandId": 1
}
```

---

### 2. READ ALL - Listar Todos os Celulares
**GET** `/phones`

**Expected Response (200):**
```json
[
  {
    "id": 1,
    "model": "Galaxy S23",
    "image": "https://example.com/galaxy-s23.jpg",
    "releaseDate": "2023-02-01",
    "price": "3999.99",
    "category": "Smartphone",
    "brandId": 1,
    "brand": {
      "id": 1,
      "name": "Samsung",
      "country": "Coreia do Sul"
    },
    "accessories": [...]
  }
]
```

---

### 3. READ ONE - Buscar Celular por ID
**GET** `/phones/1`

**Expected Response (200):**
```json
{
  "id": 1,
  "model": "Galaxy S23",
  "image": "https://example.com/galaxy-s23.jpg",
  "releaseDate": "2023-02-01",
  "price": "3999.99",
  "category": "Smartphone",
  "brandId": 1,
  "brand": {
    "id": 1,
    "name": "Samsung",
    "country": "Coreia do Sul"
  },
  "accessories": [...]
}
```

---

### 4. READ BY BRAND - Buscar Celulares por Marca
**GET** `/phones/brand/1`

**Expected Response (200):**
```json
[
  {
    "id": 1,
    "model": "Galaxy S23",
    "brandId": 1,
    "brand": {
      "id": 1,
      "name": "Samsung"
    }
  }
]
```

---

### 5. UPDATE - Atualizar Celular
**PATCH** `/phones/1`

**Headers:**
```
Content-Type: application/json
```

**Body (JSON):**
```json
{
  "price": 3499.99,
  "image": "https://example.com/galaxy-s23-new.jpg"
}
```

**Expected Response (200):**
```json
{
  "affected": 1
}
```

---

### 6. DELETE - Deletar Celular
**DELETE** `/phones/6`

**Expected Response (200):**
```json
{
  "affected": 1
}
```

---

## 🧪 Cenários de Teste

### Teste 1: Criar celular iPhone
```json
POST /phones
{
  "model": "iPhone 16 Pro Max",
  "image": "https://example.com/iphone-16-pro-max.jpg",
  "releaseDate": "2024-09-15",
  "price": 9999.99,
  "category": "Smartphone",
  "brandId": 2
}
```

### Teste 2: Criar celular Xiaomi
```json
POST /phones
{
  "model": "Redmi Note 13 Pro",
  "image": "https://example.com/redmi-note-13-pro.jpg",
  "releaseDate": "2024-03-01",
  "price": 1799.99,
  "category": "Smartphone",
  "brandId": 3
}
```

### Teste 3: Buscar celulares da Apple
```
GET /phones/brand/2
```

### Teste 4: Buscar celulares da Samsung
```
GET /phones/brand/1
```

### Teste 5: Atualizar preço
```json
PATCH /phones/2
{
  "price": 7499.99
}
```

### Teste 6: Tentar criar celular com marca inexistente
```json
POST /phones
{
  "model": "Celular Teste",
  "image": "https://example.com/teste.jpg",
  "releaseDate": "2024-01-01",
  "price": 1000.00,
  "category": "Smartphone",
  "brandId": 999
}
```
**Expected:** Erro de foreign key

---

## 🚨 **REGRAS DE NEGÓCIO - Testes de Exception**

### **REGRA 2: Não permitir celulares com mesmo modelo da mesma marca**

#### Teste A: Criar modelo duplicado na mesma marca (deve dar erro 409)
1. **Primeiro, crie um celular:**
```json
POST /phones
{
  "model": "Galaxy Teste",
  "image": "https://example.com/galaxy-teste.jpg",
  "releaseDate": "2024-01-01",
  "price": 2999.99,
  "category": "Smartphone",
  "brandId": 1
}
```

2. **Depois, tente criar o mesmo modelo na mesma marca:**
```json
POST /phones
{
  "model": "Galaxy Teste",
  "image": "https://example.com/galaxy-teste-2.jpg",
  "releaseDate": "2024-02-01",
  "price": 3299.99,
  "category": "Smartphone",
  "brandId": 1
}
```
**Expected Response (409 Conflict):**
```json
{
  "statusCode": 409,
  "message": "Celular 'Galaxy Teste' já existe para esta marca",
  "error": "Conflict"
}
```

#### Teste B: Mesmo modelo em marcas diferentes (deve funcionar)
```json
POST /phones
{
  "model": "Galaxy Teste",
  "image": "https://example.com/galaxy-teste-apple.jpg",
  "releaseDate": "2024-01-01",
  "price": 2999.99,
  "category": "Smartphone",
  "brandId": 2
}
```
**Expected:** Sucesso (marcas diferentes)

### **REGRA 7: Não permitir redução de preço superior a 50%**

#### Teste C: Redução de preço acima de 50% (deve dar erro 400)
1. **Primeiro, crie um celular com preço alto:**
```json
POST /phones
{
  "model": "iPhone Teste Preço",
  "image": "https://example.com/iphone-teste.jpg",
  "releaseDate": "2024-01-01",
  "price": 10000.00,
  "category": "Smartphone",
  "brandId": 2
}
```

2. **Depois, tente reduzir o preço em mais de 50%:**
```json
PATCH /phones/[ID_DO_CELULAR_CRIADO]
{
  "price": 4000.00
}
```
**Expected Response (400 Bad Request):**
```json
{
  "statusCode": 400,
  "message": "Redução de preço de 60.0% não permitida. Máximo permitido: 50%",
  "error": "Bad Request"
}
```

#### Teste D: Redução de preço dentro do limite (deve funcionar)
```json
PATCH /phones/[ID_DO_CELULAR_CRIADO]
{
  "price": 6000.00
}
```
**Expected:** Sucesso (redução de 40%)

#### Teste E: Aumento de preço (deve funcionar)
```json
PATCH /phones/[ID_DO_CELULAR_CRIADO]
{
  "price": 12000.00
}
```
**Expected:** Sucesso (aumento permitido)
