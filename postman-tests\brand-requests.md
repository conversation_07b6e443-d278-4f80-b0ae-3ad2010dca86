# 🏷️ Brand (Marcas) - Requisições Postman

## Base URL
```
http://localhost:3000
```

## 📋 CRUD Operations

### 1. CREATE - Criar Nova <PERSON>a
**POST** `/brands`

**Headers:**
```
Content-Type: application/json
```

**Body (JSON):**
```json
{
  "name": "OnePlus",
  "country": "China"
}
```

**Expected Response (201):**
```json
{
  "id": 6,
  "name": "OnePlus",
  "country": "China"
}
```

---

### 2. READ ALL - Listar Todas as Marcas
**GET** `/brands`

**Expected Response (200):**
```json
[
  {
    "id": 1,
    "name": "Samsung",
    "country": "Coreia do Sul",
    "phones": [...]
  },
  {
    "id": 2,
    "name": "Apple",
    "country": "Estados Unidos",
    "phones": [...]
  }
]
```

---

### 3. READ ONE - Buscar Marca por ID
**GET** `/brands/1`

**Expected Response (200):**
```json
{
  "id": 1,
  "name": "Samsung",
  "country": "Coreia do Sul",
  "phones": [
    {
      "id": 1,
      "model": "Galaxy S23",
      "price": "3999.99"
    }
  ]
}
```

---

### 4. UPDATE - Atualizar Marca
**PATCH** `/brands/1`

**Headers:**
```
Content-Type: application/json
```

**Body (JSON):**
```json
{
  "name": "Samsung Electronics",
  "country": "Coreia do Sul"
}
```

**Expected Response (200):**
```json
{
  "affected": 1
}
```

---

### 5. DELETE - Deletar Marca
**DELETE** `/brands/6`

**Expected Response (200):**
```json
{
  "affected": 1
}
```

---

## 🧪 Cenários de Teste

### Teste 1: Criar múltiplas marcas
```json
POST /brands
{
  "name": "Huawei",
  "country": "China"
}
```

```json
POST /brands
{
  "name": "Sony",
  "country": "Japão"
}
```

```json
POST /brands
{
  "name": "Nokia",
  "country": "Finlândia"
}
```

### Teste 2: Buscar marca inexistente
```
GET /brands/999
```
**Expected:** 404 ou null

### Teste 3: Atualizar apenas um campo
```json
PATCH /brands/1
{
  "country": "República da Coreia"
}
```

### Teste 4: Tentar criar marca sem dados obrigatórios
```json
POST /brands
{
  "name": "Marca Incompleta"
}
```
**Expected:** Erro de validação

### Teste 5: Atualizar marca inexistente
```json
PATCH /brands/999
{
  "name": "Marca Inexistente"
}
```
**Expected:** 404 ou affected: 0
